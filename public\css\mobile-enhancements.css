/* ===== ADVANCED MOBILE ENHANCEMENTS ===== */

/* Progressive Web App Support */
@media (max-width: 768px) {
  
  /* Better viewport handling */
  html {
    -webkit-text-size-adjust: 100% !important;
    -ms-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
  }
  
  /* Prevent horizontal scrolling */
  body {
    width: 100% !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }
  
  /* Better container handling */
  .container,
  .container-fluid {
    max-width: 100% !important;
    overflow-x: hidden !important;
  }
}

/* Enhanced Touch Interactions */
@media (hover: none) and (pointer: coarse) {
  
  /* Better touch feedback */
  .btn:active,
  .nav-link:active,
  .card:active {
    background-color: rgba(253, 34, 106, 0.1) !important;
    transform: scale(0.98) !important;
  }
  
  /* Improved swipe gestures */
  .carousel-item {
    touch-action: pan-y !important;
  }
  
  /* Better scroll behavior */
  .table-responsive {
    -webkit-overflow-scrolling: touch !important;
    scroll-behavior: smooth !important;
  }
}

/* Dark Mode Mobile Enhancements */
@media (max-width: 768px) {
  
  body.dark-mode {
    background-color: #121212 !important;
    color: #ffffff !important;
  }
  
  body.dark-mode .navbar,
  body.dark-mode .navv-bar {
    background: rgba(18, 18, 18, 0.95) !important;
    border-bottom: 1px solid #333 !important;
  }
  
  body.dark-mode .modal-content {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
  }
  
  body.dark-mode .alert {
    background-color: #2d2d2d !important;
    border-color: #444 !important;
    color: #ffffff !important;
  }
}

/* Performance Optimizations */
@media (max-width: 768px) {
  
  /* Optimize animations */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* Better image loading */
  img {
    loading: lazy !important;
    decoding: async !important;
  }
  
  /* Optimize fonts */
  @font-face {
    font-display: swap !important;
  }
}

/* Accessibility Improvements */
@media (max-width: 768px) {
  
  /* Better contrast */
  .text-muted {
    color: #666 !important;
  }
  
  body.dark-mode .text-muted {
    color: #aaa !important;
  }
  
  /* Improved focus management */
  .skip-link {
    position: absolute !important;
    top: -40px !important;
    left: 6px !important;
    background: #fd226a !important;
    color: white !important;
    padding: 8px !important;
    text-decoration: none !important;
    border-radius: 4px !important;
    z-index: 9999 !important;
  }
  
  .skip-link:focus {
    top: 6px !important;
  }
  
  /* Better screen reader support */
  .sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
  }
}

/* Enhanced Loading States */
@media (max-width: 768px) {
  
  /* Loading spinner */
  .loading {
    display: inline-block !important;
    width: 20px !important;
    height: 20px !important;
    border: 3px solid rgba(253, 34, 106, 0.3) !important;
    border-radius: 50% !important;
    border-top-color: #fd226a !important;
    animation: spin 1s ease-in-out infinite !important;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* Skeleton loading */
  .skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
    background-size: 200% 100% !important;
    animation: loading 1.5s infinite !important;
  }
  
  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
  
  body.dark-mode .skeleton {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%) !important;
    background-size: 200% 100% !important;
  }
}

/* Better Error Handling */
@media (max-width: 768px) {
  
  .error-message {
    background-color: #fee !important;
    border: 1px solid #fcc !important;
    color: #c33 !important;
    padding: 15px !important;
    border-radius: 8px !important;
    margin: 10px 0 !important;
    font-size: 14px !important;
  }
  
  body.dark-mode .error-message {
    background-color: #4a1a1a !important;
    border-color: #8a3a3a !important;
    color: #ff6b6b !important;
  }
  
  .success-message {
    background-color: #efe !important;
    border: 1px solid #cfc !important;
    color: #3c3 !important;
    padding: 15px !important;
    border-radius: 8px !important;
    margin: 10px 0 !important;
    font-size: 14px !important;
  }
  
  body.dark-mode .success-message {
    background-color: #1a4a1a !important;
    border-color: #3a8a3a !important;
    color: #6bff6b !important;
  }
}

/* Enhanced Typography */
@media (max-width: 768px) {
  
  /* Better text scaling */
  .display-1 { font-size: 2.5rem !important; }
  .display-2 { font-size: 2rem !important; }
  .display-3 { font-size: 1.75rem !important; }
  .display-4 { font-size: 1.5rem !important; }
  
  /* Improved readability */
  p, .lead {
    max-width: 100% !important;
    word-wrap: break-word !important;
    hyphens: auto !important;
  }
  
  /* Better list styling */
  ul, ol {
    padding-left: 20px !important;
  }
  
  li {
    margin-bottom: 8px !important;
    line-height: 1.5 !important;
  }
}

/* Print Styles for Mobile */
@media print {
  
  .navbar,
  .nav-links,
  .menu-btn,
  .btn,
  .modal,
  .alert {
    display: none !important;
  }
  
  body {
    font-size: 12pt !important;
    line-height: 1.4 !important;
    color: black !important;
    background: white !important;
  }
  
  .container {
    max-width: 100% !important;
    padding: 0 !important;
  }
}

/* Utility Classes for Mobile */
@media (max-width: 768px) {
  
  .mobile-center { text-align: center !important; }
  .mobile-left { text-align: left !important; }
  .mobile-right { text-align: right !important; }
  
  .mobile-hide { display: none !important; }
  .mobile-show { display: block !important; }
  
  .mobile-full-width { width: 100% !important; }
  .mobile-auto-width { width: auto !important; }
  
  .mobile-no-padding { padding: 0 !important; }
  .mobile-small-padding { padding: 10px !important; }
  .mobile-medium-padding { padding: 20px !important; }
  
  .mobile-no-margin { margin: 0 !important; }
  .mobile-small-margin { margin: 10px !important; }
  .mobile-medium-margin { margin: 20px !important; }
}
