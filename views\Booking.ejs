<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <!-- Google Fonts -->
    <link
      href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap"
      rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Belleza&display=swap"
      rel="stylesheet">

    <!-- Bootstrap -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/public.css">
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link href="https://fonts.googleapis.com/css2?family=Belleza&display=swap"
      rel="stylesheet">

    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
      rel="stylesheet">
    <link
      href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap"
      rel="stylesheet">
    <link rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="/css/darkMode.css">
    <link rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <style>
        :root {
            --primary: #4A90E2;
            --primary-foreground: #FFFFFF;
            --secondary: #50E3C2;
            --secondary-foreground: #000000;
            --accent: #FF7F50;
            --accent-foreground: #FFFFFF;
            --background: #F8F9FA;
            --foreground: #212529;
            --card: #FFFFFF;
            --card-foreground: #212529;
            --border: #DEE2E6;
            --input: #E9ECEF;
            --ring: #4A90E2;
            --radius: 0.5rem;
            --shadow: 0 4px 6px -1px rgba(0,0,0,0.1);
            --gold: goldenrod;
        }

        body {
            color: var(--foreground);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: var(--background);
        }

        /* Navbar Styles */
        .navv-bar {
            font-family: 'Belleza', sans-serif;
        }

        .logo {
            font-family: 'Dancing Script', cursive;
            font-weight: 700;
            color: var(--gold);
        }

        .nav-links ul {
            display: flex;
            gap: 1.5rem;
        }

        .nav-link {
            color: var(--foreground);
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 1.1rem;
        }

            .nav-link:hover {
                color: var(--gold);
            }

        .menu-btn {
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Card & Form Styles */
        .card {
            background: var(--card);
            border-radius: var(--radius);
            box-shadow: var(--shadow);
            border: none;
        }

        .form-control {
            border-color: var(--border);
            border-radius: var(--radius);
            padding: 0.75rem 1rem;
            font-size: 1rem;
        }

            .form-control:focus {
                border-color: var(--ring);
                box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
            }

        .btn-gold {
            background-color: var(--gold);
            border-color: var(--gold);
            color: white;
            transition: all 0.3s ease;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
        }

            .btn-gold:hover {
                background-color: #c7931a;
                border-color: #c7931a;
                color: white;
            }

        .page-title {
            font-size: 2.2rem;
            text-align: center;
            margin-bottom: 1rem;
            color: goldenrod;
            font-weight: 600;
            font-family: 'Dancing Script', cursive;
        }

        .page-subtitle {
            font-size: 1.2rem;
            text-align: center;
            margin-bottom: 2.5rem;
            color: #333;
            font-family: 'Belleza', sans-serif;
        }

        .section-title {
            font-size: 1.3rem;
            color: var(--gold);
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid var(--border);
            font-family: 'Dancing Script', cursive;
        }

        /* Footer Styles */
        footer {
            background-color: #212529;
            color: goldenrod;
            font-family: 'Belleza', sans-serif;
            border-top: 2px solid var(--border);
            margin-top: 3rem;
        }

            footer h2 {
                font-family: 'Dancing Script', cursive;
                font-weight: 700;
            }

            footer h5 {
                font-weight: 600;
                letter-spacing: 1px;
                border-bottom: 1px solid goldenrod;
                padding-bottom: 0.5rem;
                display: inline-block;
            }

            footer a {
                color: goldenrod;
                text-decoration: none;
                transition: all 0.3s ease;
            }

                footer a:hover {
                    opacity: 0.8;
                    padding-left: 3px;
                }

            footer .social-links a {
                font-size: 1.2rem;
                transition: transform 0.3s ease;
            }

                footer .social-links a:hover {
                    transform: translateY(-3px);
                }

        @media (max-width: 768px) {
            .nav-links ul {
                flex-direction: column;
                gap: 1rem;
            }

            .page-title {
                font-size: 1.8rem;
            }

            .page-subtitle {
                font-size: 1rem;
            }
        }
    </style>
    <title>Booking | Decore&More</title>
    <link rel="stylesheet" href="/css/darkMode.css">
    <script src="/js/darkMode.js"></script>
  </head>
  <body>
    <style>

    .dropdown {
        position: relative;
    }

    .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        display: none;
        flex-direction: column;
        min-width: 150px;
    }

    .dropdown-menu a {
        display: block;
        padding: 10px;
        color: black;
        text-decoration: none;
    }

    .dropdown-menu a:hover {
        background: #f4f4f4;
    }


    .dropdown:hover .dropdown-menu {
        display: flex;
    }

    
      .logo-hero {
    background: linear-gradient(90deg, #d4af37 0%, #ffd700 50%, #bfa14a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: bold;
}

    </style>
    <!-- Navbar -->
    <div class="container" data-aos="zoom-in" data-aos-duration="1200">
      <div class="row">
        <div
          class="navv-bar py-4 d-flex justify-content-between align-items-center">
          <div class="logo d-flex align-items-center" style="font-size: 35px;"
            data-aos="fade-down"
            data-aos-duration="1200">

            <button class="btn btn-outline-dark ms-auto " onclick="goBack()">
              <i class="fa-solid fa-arrow-left"></i> Back
            </button>

            <span class="logo-hero  ms-3">Decore&More</span>
          </div>
          <div class="menu-btn d-lg-none" data-aos="fade-up"
            data-aos-duration="1200">
            <i class="fa-solid fa-bars"></i>
          </div>
          <div class="nav-links" data-aos="fade-up" data-aos-duration="1200">
            <ul class="list-unstyled m-0">
              <li><a href="/" class="nav-link">Home</a></li>
              <li><a href="/#op" class="nav-link">About</a></li>
              <li class="dropdown">
                <a href="/#od" class="nav-link">Services</a>
                <div class="dropdown-menu">
                  <a href="/packages/by-occasion?occasion=Birthday"
                    class="nav-link">Birthday</a>
                  <a href="/packages/by-occasion?occasion=Wedding"
                    class="nav-link">Wedding</a>
                  <a href="/packages/by-occasion?occasion=Engagement"
                    class="nav-link">Engagement</a>
                  <a href="/packages/by-occasion?occasion=BabyShower"
                    class="nav-link">BabyShower</a>
                </div>
              </li>
              <li><a href="/designers" class="nav-link">Designers</a></li>
              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Register</a>
                <div class="dropdown-menu">
                  <a href="/registerCustomer">Customer</a>
                  <a href="/register">Engineer</a>
                </div>
              </li>
              <% } %>

              <% if (!user || !user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link">Login</a>
                <div class="dropdown-menu">
                  <a href="/login">Customer</a>
                  <a href="/login">Engineer</a>
                </div>
              </li>
              <% } %>

              <li><a href="/contact" class="nav-link">Contact</a></li>

              <% if (user && user.name) { %>
              <li class="dropdown">
                <a href="#" class="nav-link d-flex align-items-center">
                  <i class="fa-solid fa-user"></i>
                  <span><%= user.name %></span>
                </a>
                <div class="dropdown-menu">
                  <a href="/userProfile/<%= user.id %>" class="nav-link">
                    <i class="fas fa-user me-2"></i>Profile
                  </a>
                  <a href="#" class="nav-link" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                  </a>
                </div>
              </li>
              <% }else{ %>

              <% } %>
              <button onclick="toggleDarkMode()" class="dark-mode"
                aria-label="Toggle dark mode">
                <i class="fa-solid fa-moon" aria-hidden="true"
                  title="Toggle to dark mode"></i>
              </button>

            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Booking Form -->
    <div class="container py-5">
      <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
          <div class="card shadow-lg">
            <div class="card-body p-4 p-md-5">
              <h1 class="page-title section-gradient-title">Booking Page</h1>
              <p class="page-subtitle">Let us create unforgettable moments for
                your special occasion</p>

              <h4 class="section-title section-gradient-title">Personal
                Information</h4>
              <form id="bookingForm" class="needs-validation" novalidate
                method="POST" action="/booking"
                data-engineer-id="<%= typeof package !== 'undefined' && package !== null ? package.engID : '' %>">
                <input type="hidden" name="packageId"
                  value="<%= typeof packageId !== 'undefined' ? packageId : (typeof package !== 'undefined' && package !== null ? package._id : (typeof packageID !== 'undefined' ? packageID : '')) %>">
                <div class="row g-4">
                  <div class="col-md-4">
                    <label class="form-label">Name</label>
                    <input type="text" class="form-control"
                      value="<%= client?.name || '' %>" readonly>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control"
                      value="<%= client?.email || '' %>" readonly>
                  </div>
                  <div class="col-md-4">
                    <label class="form-label">Phone</label>
                    <input type="text" class="form-control"
                      value="<%= client?.phone || '' %>" readonly>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Package Name</label>
                    <input type="text" class="form-control"
                      value="<%= package?.name || '' %>" readonly>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Package Price</label>
                    <input type="text" class="form-control"
                      value="<%= package?.price ? package.price + ' EGP' : '' %>"
                      readonly>
                  </div>

                  <div class="col-md-6">
                    <label class="form-label">Event Date</label>
                    <input type="date" class="form-control" name="eventDate"
                      required placeholder="mm/dd/yyyy">
                  </div>

                  <div class="col-md-6">
                    <label class="form-label">Event Type</label>
                    <select class="form-control" required name="eventType">
                      <option value>Select event type</option>
                      <option value="Wedding" <%=selectedEventType === 'Wedding'
                        ? 'selected' : '' %>>Wedding</option>
                      <option value="Birthday" <%=selectedEventType ===
                        'Birthday' ? 'selected' : '' %>>Birthday</option>
                      <option value="Engagement" <%=selectedEventType ===
                        'Engagement' ? 'selected' : '' %>>Engagement</option>
                      <option value="BabyShower" <%=selectedEventType ===
                        'BabyShower' ? 'selected' : '' %>>BabyShower</option>
                    </select>
                  </div>

                  <div class="col-12">
                    <label class="form-label">Event Location</label>
                    <input type="text" class="form-control" name="eventLocation"
                      required placeholder="Enter event address">
                  </div>

                  <div class="col-12">
                    <label class="form-label">Special Requests</label>
                    <textarea class="form-control" rows="3"
                      name="specialRequests"
                      placeholder="Tell us about your theme, colors, or any special requirements"></textarea>
                  </div>

                  <div class="col-12 mt-4">
                    <div class="form-check">
                      <input class="form-check-input" type="checkbox" required
                        id="terms">
                      <label class="form-check-label" for="terms">
                        I agree to the <a href="#"
                          style="color: var(--gold);">terms and conditions</a>
                      </label>
                    </div>
                  </div>

                  <div class="col-12 text-center mt-4">
                    <button type="submit" class="btn btn-gold px-5 py-2">
                      Book Now
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->

    <footer class="bg-dark text-warning py-5">
      <div class="container">
        <div class="row g-4">
          <div class="col-12 text-center mb-4">
            <h2 class="display-4 fw-bold">Decor&More</h2>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Contact Us</h5>
            <div class="mt-3">
              <p><i
                  class="bi bi-envelope-fill me-2"></i><EMAIL></p>
              <a href="https://web.whatsapp.com/send?phone=201556159175"
                target="_blank">
                <p><i class="bi bi-telephone-fill me-2"></i>+20 1556159175</p>
              </a>
              <p><i class="bi bi-geo-alt-fill me-2"></i>123 Decor Street,
                Design
                City</p>
            </div>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Quick Links</h5>
            <ul class="list-unstyled mt-3">
              <li class="mb-2"><a href="/"
                  class="text-warning text-decoration-none">Home</a></li>
              <li class="mb-2"><a href="/#op"
                  class="text-warning text-decoration-none">About</a></li>
              <% if (!user) { %>
              <li class="mb-2"><a href="/login"
                  class="text-warning text-decoration-none">login</a></li>
              <li class="mb-2"><a href="/register"
                  class="text-warning text-decoration-none">register</a></li>
              <% } %>
            </ul>
          </div>
          <div class="col-md-4">
            <h5 class="border-bottom border-warning pb-2">Follow Us</h5>
            <div class="mt-3 d-flex gap-3">
              <a href="#" class="text-warning fs-4" aria-label="Facebook"><i
                  class="bi bi-facebook"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Instagram"><i
                  class="bi bi-instagram"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="Twitter"><i
                  class="bi bi-twitter-x"></i></a>
              <a href="#" class="text-warning fs-4" aria-label="LinkedIn"><i
                  class="bi bi-linkedin"></i></a>
            </div>
          </div>
          <div class="col-12">
            <hr class="border-warning">
            <p class="text-center mb-0">&copy; 2024 Decor&More. All rights
              reserved.</p>
          </div>
        </div>
      </div>
    </footer>
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/darkMode.js"></script>
    <script>
    function logout() {
      fetch("/logout", { method: "POST" }).then(() => (window.location.href = "/"));
    }

    function goBack() {
      // Check if we have booking information in localStorage
      const packageId = localStorage.getItem('bookingPackageId');
      const engineerId = localStorage.getItem('bookingEngineerId');

      if (packageId && engineerId) {
        // If we have booking info, go back to the engineer's profile page
        window.location.href = `/profile/${engineerId}`;
      } else {
        // Otherwise, try to go back in history
        if (document.referrer && document.referrer.includes(window.location.hostname)) {
          window.location.href = document.referrer;
        } else {
          // If no referrer or external referrer, go to home page
          window.location.href = '/';
        }
      }
    }
  </script>
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Booking Availability Checker -->
    <script src="/js/booking-availability.js"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Form validation with availability check
            const form = document.getElementById('bookingForm');
            form.addEventListener('submit', async function (event) {
                event.preventDefault(); // Always prevent default first

                if (!form.checkValidity()) {
                    event.stopPropagation();
                    form.classList.add('was-validated');
                    return;
                }

                // Check engineer availability before submitting
                const dateInput = form.querySelector('input[name="eventDate"]');
                const engineerId = form.getAttribute('data-engineer-id');

                if (dateInput && dateInput.value && engineerId) {
                    try {
                        const response = await fetch('/booking/check-availability', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                engineerId: engineerId,
                                eventDate: dateInput.value
                            })
                        });

                        const result = await response.json();

                        if (!result.available) {
                            // Show error message under date input
                            bookingChecker.showDateUnavailableMessage(dateInput.value, result.message);
                            dateInput.value = ''; // Clear the invalid date
                            return;
                        }
                    } catch (error) {
                        console.error('Error checking availability:', error);
                        alert('Error checking availability. Please try again.');
                        return;
                    }
                }

                // If we get here, everything is valid - submit the form
                form.classList.add('was-validated');

                // Create FormData and submit via fetch to handle JSON responses
                const formData = new FormData(form);

                try {
                    const response = await fetch(form.action, {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok) {
                        // If successful, redirect to payment or success page
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            const result = await response.json();
                            if (result.redirectUrl) {
                                window.location.href = result.redirectUrl;
                            }
                        } else {
                            // If it's HTML response, replace current page
                            const html = await response.text();
                            document.open();
                            document.write(html);
                            document.close();
                        }
                    } else {
                        // Handle error response
                        const errorData = await response.json();
                        if (errorData.error === 'ENGINEER_NOT_AVAILABLE') {
                            bookingChecker.showDateUnavailableMessage(dateInput.value, errorData.message);
                            dateInput.value = '';
                        } else {
                            alert(errorData.message || 'An error occurred. Please try again.');
                        }
                    }
                } catch (error) {
                    console.error('Error submitting form:', error);
                    alert('An error occurred. Please try again.');
                }
            }, false);

            // Mobile menu toggle
            const menuBtn = document.querySelector('.menu-btn');
            const navLinks = document.querySelector('.nav-links');

            if (menuBtn && navLinks) {
                menuBtn.addEventListener('click', function () {
                    navLinks.classList.toggle('d-none');
                    navLinks.classList.toggle('d-block');
                });
            }
        });
    </script>
  </body>
</html>