<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم المتجاوب - Decor & More</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet" href="/css/mobile-responsive.css">
    <link rel="stylesheet" href="/css/mobile-enhancements.css">
    
    <style>
        .test-section {
            padding: 40px 0;
            border-bottom: 1px solid #eee;
        }
        
        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .test-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .test-button {
            margin: 5px;
            min-height: 44px;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1 class="text-center mb-4">🧪 اختبار التصميم المتجاوب</h1>
        
        <!-- Device Information -->
        <div class="device-info">
            <h3>معلومات الجهاز</h3>
            <p><strong>عرض الشاشة:</strong> <span id="screenWidth"></span>px</p>
            <p><strong>ارتفاع الشاشة:</strong> <span id="screenHeight"></span>px</p>
            <p><strong>نوع الجهاز:</strong> <span id="deviceType"></span></p>
            <p><strong>المتصفح:</strong> <span id="browserInfo"></span></p>
            <p><strong>دعم اللمس:</strong> <span id="touchSupport"></span></p>
        </div>
        
        <!-- Navigation Test -->
        <div class="test-section">
            <h2>🧭 اختبار التنقل</h2>
            <div class="test-card">
                <h4>قائمة التنقل المحمولة</h4>
                <div class="navv-bar py-4 d-flex justify-content-between align-items-center">
                    <div class="logo">
                        <span class="fw-bold text-warning">Decor&More</span>
                    </div>
                    <div class="menu-btn d-lg-none">
                        <i class="fa-solid fa-bars"></i>
                    </div>
                    <div class="nav-links">
                        <ul class="list-unstyled m-0">
                            <li><a href="#" class="nav-link">الرئيسية</a></li>
                            <li><a href="#" class="nav-link">حول</a></li>
                            <li><a href="#" class="nav-link">الخدمات</a></li>
                            <li><a href="#" class="nav-link">اتصل بنا</a></li>
                        </ul>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="status-indicator" id="navStatus"></span>
                    <span id="navStatusText">جاري الاختبار...</span>
                </div>
            </div>
        </div>
        
        <!-- Button Test -->
        <div class="test-section">
            <h2>🔘 اختبار الأزرار</h2>
            <div class="test-card">
                <h4>أحجام الأزرار المختلفة</h4>
                <button class="btn btn-primary test-button">زر أساسي</button>
                <button class="btn btn-secondary test-button">زر ثانوي</button>
                <button class="btn btn-success test-button">زر نجاح</button>
                <button class="btn btn-warning test-button">زر تحذير</button>
                <div class="mt-3">
                    <span class="status-indicator" id="buttonStatus"></span>
                    <span id="buttonStatusText">جاري الاختبار...</span>
                </div>
            </div>
        </div>
        
        <!-- Form Test -->
        <div class="test-section">
            <h2>📝 اختبار النماذج</h2>
            <div class="test-card">
                <h4>نموذج اختبار</h4>
                <form id="testForm">
                    <div class="form-group mb-3">
                        <label class="form-label">الاسم</label>
                        <input type="text" class="form-control" placeholder="أدخل اسمك">
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" placeholder="أدخل بريدك الإلكتروني">
                    </div>
                    <div class="form-group mb-3">
                        <label class="form-label">الرسالة</label>
                        <textarea class="form-control" rows="3" placeholder="اكتب رسالتك هنا"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">إرسال</button>
                </form>
                <div class="mt-3">
                    <span class="status-indicator" id="formStatus"></span>
                    <span id="formStatusText">جاري الاختبار...</span>
                </div>
            </div>
        </div>
        
        <!-- Card Test -->
        <div class="test-section">
            <h2>🃏 اختبار البطاقات</h2>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card test-card">
                        <div class="card-body">
                            <h5 class="card-title">بطاقة اختبار 1</h5>
                            <p class="card-text">هذا نص تجريبي لاختبار البطاقة على الأجهزة المحمولة.</p>
                            <a href="#" class="btn btn-primary">اقرأ المزيد</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card test-card">
                        <div class="card-body">
                            <h5 class="card-title">بطاقة اختبار 2</h5>
                            <p class="card-text">هذا نص تجريبي آخر لاختبار البطاقة.</p>
                            <a href="#" class="btn btn-secondary">اقرأ المزيد</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card test-card">
                        <div class="card-body">
                            <h5 class="card-title">بطاقة اختبار 3</h5>
                            <p class="card-text">هذا نص تجريبي ثالث لاختبار البطاقة.</p>
                            <a href="#" class="btn btn-success">اقرأ المزيد</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3">
                <span class="status-indicator" id="cardStatus"></span>
                <span id="cardStatusText">جاري الاختبار...</span>
            </div>
        </div>
        
        <!-- Test Results -->
        <div class="test-section">
            <h2>📊 نتائج الاختبار</h2>
            <div class="test-card">
                <h4>ملخص النتائج</h4>
                <div id="testResults">
                    <p>جاري تشغيل الاختبارات...</p>
                </div>
                <button class="btn btn-primary" onclick="runAllTests()">إعادة تشغيل الاختبارات</button>
            </div>
        </div>
    </div>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
    
    <script>
        // Test functions
        function updateDeviceInfo() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
            document.getElementById('screenHeight').textContent = window.innerHeight;
            
            const isMobile = window.innerWidth <= 768;
            const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
            const isDesktop = window.innerWidth > 1024;
            
            let deviceType = 'غير محدد';
            if (isMobile) deviceType = 'هاتف محمول';
            else if (isTablet) deviceType = 'جهاز لوحي';
            else if (isDesktop) deviceType = 'حاسوب مكتبي';
            
            document.getElementById('deviceType').textContent = deviceType;
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ')[0];
            document.getElementById('touchSupport').textContent = 'ontouchstart' in window ? 'مدعوم' : 'غير مدعوم';
        }
        
        function testNavigation() {
            const menuBtn = document.querySelector('.menu-btn');
            const navLinks = document.querySelector('.nav-links');
            const isMobile = window.innerWidth <= 768;
            
            let status = 'pass';
            let message = 'التنقل يعمل بشكل صحيح';
            
            if (isMobile) {
                if (!menuBtn || !navLinks) {
                    status = 'fail';
                    message = 'عناصر التنقل المحمولة مفقودة';
                } else if (getComputedStyle(menuBtn).display === 'none') {
                    status = 'fail';
                    message = 'زر القائمة غير ظاهر على الموبايل';
                }
            }
            
            updateTestStatus('nav', status, message);
        }
        
        function testButtons() {
            const buttons = document.querySelectorAll('.test-button');
            let status = 'pass';
            let message = 'جميع الأزرار تعمل بشكل صحيح';
            
            buttons.forEach(button => {
                const height = button.offsetHeight;
                if (height < 44) {
                    status = 'warning';
                    message = 'بعض الأزرار أصغر من الحد الأدنى المطلوب (44px)';
                }
            });
            
            updateTestStatus('button', status, message);
        }
        
        function testForms() {
            const inputs = document.querySelectorAll('#testForm input, #testForm textarea');
            let status = 'pass';
            let message = 'النماذج محسنة للموبايل';
            
            inputs.forEach(input => {
                const fontSize = parseFloat(getComputedStyle(input).fontSize);
                if (fontSize < 16) {
                    status = 'warning';
                    message = 'حجم خط بعض الحقول أصغر من 16px (قد يسبب تكبير في iOS)';
                }
            });
            
            updateTestStatus('form', status, message);
        }
        
        function testCards() {
            const cards = document.querySelectorAll('.card');
            let status = 'pass';
            let message = 'البطاقات تعرض بشكل صحيح';
            
            if (window.innerWidth <= 768) {
                cards.forEach(card => {
                    const width = card.offsetWidth;
                    const containerWidth = card.parentElement.offsetWidth;
                    if (width > containerWidth) {
                        status = 'fail';
                        message = 'بعض البطاقات تتجاوز عرض الحاوية';
                    }
                });
            }
            
            updateTestStatus('card', status, message);
        }
        
        function updateTestStatus(testType, status, message) {
            const statusElement = document.getElementById(testType + 'Status');
            const textElement = document.getElementById(testType + 'StatusText');
            
            statusElement.className = 'status-indicator status-' + status;
            textElement.textContent = message;
        }
        
        function runAllTests() {
            updateDeviceInfo();
            testNavigation();
            testButtons();
            testForms();
            testCards();
            
            // Update results summary
            setTimeout(() => {
                const results = document.getElementById('testResults');
                const passCount = document.querySelectorAll('.status-pass').length;
                const warningCount = document.querySelectorAll('.status-warning').length;
                const failCount = document.querySelectorAll('.status-fail').length;
                
                results.innerHTML = `
                    <p><strong>✅ اختبارات ناجحة:</strong> ${passCount}</p>
                    <p><strong>⚠️ تحذيرات:</strong> ${warningCount}</p>
                    <p><strong>❌ اختبارات فاشلة:</strong> ${failCount}</p>
                    <p><strong>النتيجة الإجمالية:</strong> ${failCount === 0 ? 'ممتاز' : warningCount > 0 ? 'جيد' : 'يحتاج تحسين'}</p>
                `;
            }, 500);
        }
        
        // Run tests on load and resize
        window.addEventListener('load', runAllTests);
        window.addEventListener('resize', runAllTests);
        
        // Prevent form submission for testing
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('تم اختبار النموذج بنجاح!');
        });
    </script>
</body>
</html>
