<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ - Decor & More</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/public.css">
    <link rel="stylesheet" href="/css/mobile-responsive.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Arial', sans-serif;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            margin: 2rem;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #fd226a;
            margin-bottom: 1.5rem;
        }
        
        .error-title {
            color: #333;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        
        .error-message {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .error-details {
            color: #888;
            font-size: 1rem;
            margin-bottom: 2rem;
        }
        
        .btn-back {
            background: linear-gradient(45deg, #fd226a, #ff6b9d);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(253, 34, 106, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .btn-secondary {
            background: #6c757d;
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .suggestions {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: right;
        }
        
        .suggestions h5 {
            color: #fd226a;
            margin-bottom: 1rem;
        }
        
        .suggestions ul {
            list-style: none;
            padding: 0;
        }
        
        .suggestions li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .suggestions li:last-child {
            border-bottom: none;
        }
        
        .suggestions i {
            color: #fd226a;
            margin-left: 0.5rem;
        }
        
        @media (max-width: 768px) {
            .error-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-icon {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h1 class="error-title">
            <%= message || 'حدث خطأ غير متوقع' %>
        </h1>
        
        <p class="error-message">
            <%= details || 'نعتذر عن هذا الإزعاج. يرجى المحاولة مرة أخرى.' %>
        </p>
        
        <% if (typeof message !== 'undefined' && message.includes('محجوز')) { %>
        <div class="suggestions">
            <h5><i class="fas fa-lightbulb"></i> اقتراحات للحل:</h5>
            <ul>
                <li><i class="fas fa-calendar-alt"></i> اختر تاريخاً آخر متاحاً</li>
                <li><i class="fas fa-user-tie"></i> اختر مهندساً آخر لنفس المناسبة</li>
                <li><i class="fas fa-phone"></i> اتصل بنا للاستفسار عن التواريخ المتاحة</li>
                <li><i class="fas fa-clock"></i> جرب الحجز في وقت لاحق</li>
            </ul>
        </div>
        <% } %>
        
        <div class="mt-4">
            <a href="javascript:history.back()" class="btn-back">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للخلف
            </a>
            
            <a href="/" class="btn-secondary">
                <i class="fas fa-home me-2"></i>
                الصفحة الرئيسية
            </a>
        </div>
        
        <% if (typeof message !== 'undefined' && message.includes('محجوز')) { %>
        <div class="mt-4">
            <a href="/designers" class="btn-back">
                <i class="fas fa-users me-2"></i>
                تصفح المهندسين الآخرين
            </a>
        </div>
        <% } %>
        
        <div class="mt-4">
            <small class="text-muted">
                إذا استمرت المشكلة، يرجى 
                <a href="/contact" style="color: #fd226a;">الاتصال بنا</a>
            </small>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto redirect after 10 seconds if no user interaction
        let redirectTimer = setTimeout(() => {
            window.location.href = '/';
        }, 10000);
        
        // Cancel auto redirect if user interacts with page
        document.addEventListener('click', () => {
            clearTimeout(redirectTimer);
        });
        
        document.addEventListener('keydown', () => {
            clearTimeout(redirectTimer);
        });
        
        // Add some animation
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.error-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.5s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
