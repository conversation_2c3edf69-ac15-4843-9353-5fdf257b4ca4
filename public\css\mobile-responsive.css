/* ===== MOBILE RESPONSIVE STYLES ===== */

/* CSS Custom Properties for Mobile */
:root {
  --vh: 1vh;
  --mobile-header-height: 70px;
  --mobile-footer-height: 200px;
}

/* Base Mobile Styles */
@media (max-width: 768px) {
  /* Typography */
  body {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }

  h1 {
    font-size: 2rem !important;
  }
  h2 {
    font-size: 1.75rem !important;
  }
  h3 {
    font-size: 1.5rem !important;
  }
  h4 {
    font-size: 1.25rem !important;
  }

  /* Container and Layout */
  .container {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }

  /* Navigation */
  .navbar {
    padding: 0.5rem 1rem !important;
  }

  .nav-links {
    width: 100% !important;
    background: rgba(255, 255, 255, 0.95) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 10px !important;
    margin-top: 10px !important;
    padding: 20px !important;
  }

  .nav-links ul {
    flex-direction: column !important;
    gap: 15px !important;
  }

  .nav-links li a {
    font-size: 18px !important;
    padding: 10px 15px !important;
    display: block !important;
    text-align: center !important;
  }

  /* Hero Section */
  .home-img {
    height: 60vh !important;
    background-size: cover !important;
    background-position: center !important;
  }

  .home-img h1 {
    font-size: 3rem !important;
    padding: 2rem 1rem !important;
    text-align: center !important;
  }

  /* Cards and Content */
  .card {
    margin-bottom: 20px !important;
    border-radius: 15px !important;
  }

  .card-body {
    padding: 1.5rem !important;
  }

  /* Forms */
  .form-control {
    font-size: 16px !important;
    padding: 12px 15px !important;
    border-radius: 10px !important;
  }

  .btn {
    font-size: 16px !important;
    padding: 12px 20px !important;
    border-radius: 10px !important;
    width: 100% !important;
    margin-bottom: 10px !important;
  }

  /* Tables */
  .table-responsive {
    border-radius: 10px !important;
    overflow-x: auto !important;
  }

  .table {
    font-size: 14px !important;
  }

  .table th,
  .table td {
    padding: 8px !important;
    white-space: nowrap !important;
  }

  /* Images */
  img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 10px !important;
  }

  /* Profile Images */
  .profile-img,
  .engineer-img {
    width: 80px !important;
    height: 80px !important;
    border-radius: 50% !important;
    object-fit: cover !important;
  }

  /* Modals */
  .modal-dialog {
    margin: 10px !important;
    max-width: calc(100% - 20px) !important;
  }

  .modal-content {
    border-radius: 15px !important;
  }

  /* Gallery */
  .gallery-item {
    margin-bottom: 15px !important;
  }

  .gallery-item img {
    height: 200px !important;
    border-radius: 10px !important;
  }

  /* Engineer Cards */
  .engineer-card {
    margin-bottom: 20px !important;
    padding: 15px !important;
    border-radius: 15px !important;
  }

  /* Package Cards */
  .package-card {
    margin-bottom: 20px !important;
    padding: 15px !important;
  }

  /* Booking Forms */
  .booking-form {
    padding: 20px !important;
    border-radius: 15px !important;
  }

  /* Footer */
  footer {
    padding: 30px 15px !important;
  }

  footer .row > div {
    margin-bottom: 30px !important;
  }

  /* Utility Classes */
  .text-center-mobile {
    text-align: center !important;
  }

  .mb-mobile {
    margin-bottom: 20px !important;
  }

  .p-mobile {
    padding: 15px !important;
  }

  /* Hide on Mobile */
  .hide-mobile {
    display: none !important;
  }

  /* Show only on Mobile */
  .show-mobile {
    display: block !important;
  }
}

/* Extra Small Devices */
@media (max-width: 576px) {
  .home-img h1 {
    font-size: 2.5rem !important;
  }

  .container {
    padding-left: 10px !important;
    padding-right: 10px !important;
  }

  .card-body {
    padding: 1rem !important;
  }

  .btn {
    font-size: 14px !important;
    padding: 10px 15px !important;
  }

  .table {
    font-size: 12px !important;
  }

  .modal-dialog {
    margin: 5px !important;
    max-width: calc(100% - 10px) !important;
  }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .home-img {
    height: 80vh !important;
  }

  .home-img h1 {
    font-size: 2.5rem !important;
  }
}

/* Touch Improvements */
@media (hover: none) and (pointer: coarse) {
  .btn,
  .nav-links a,
  .card {
    transition: none !important;
  }

  .btn:active {
    transform: scale(0.98) !important;
  }

  /* Larger touch targets */
  .nav-links a,
  .btn,
  .form-control {
    min-height: 44px !important;
  }
}

/* Dark Mode Mobile Adjustments */
@media (max-width: 768px) {
  body.dark-mode .nav-links {
    background: rgba(0, 0, 0, 0.95) !important;
    border: 1px solid #333 !important;
  }

  body.dark-mode .card {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
  }

  body.dark-mode .form-control {
    background: #2a2a2a !important;
    border: 1px solid #444 !important;
    color: #fff !important;
  }

  body.dark-mode .btn-primary {
    background: #fd226a !important;
    border-color: #fd226a !important;
  }
}

/* Fix for specific elements that might overflow */
* {
  box-sizing: border-box !important;
}

html,
body {
  overflow-x: hidden !important;
}

/* Prevent zoom on input focus (iOS) */
@media (max-width: 768px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="tel"],
  textarea,
  select {
    font-size: 16px !important;
  }
}

/* ===== ADDITIONAL MOBILE IMPROVEMENTS ===== */

/* Better spacing for mobile */
@media (max-width: 768px) {
  /* Improved section spacing */
  section {
    padding: 40px 0 !important;
  }

  /* Better card spacing */
  .row .col-md-4,
  .row .col-md-6,
  .row .col-lg-4,
  .row .col-lg-6 {
    margin-bottom: 25px !important;
  }

  /* Improved text readability */
  p,
  .lead,
  .leadd {
    font-size: 16px !important;
    line-height: 1.6 !important;
    margin-bottom: 20px !important;
  }

  /* Better button spacing */
  .btn + .btn {
    margin-top: 10px !important;
    margin-left: 0 !important;
  }

  /* Improved list spacing */
  ul li {
    margin-bottom: 10px !important;
  }

  /* Better image handling */
  .img-fluid {
    border-radius: 15px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
  }
}

/* Enhanced navigation for mobile */
@media (max-width: 768px) {
  /* Better hamburger menu */
  .menu-btn {
    font-size: 24px !important;
    padding: 10px !important;
    border: none !important;
    background: transparent !important;
    color: #fd226a !important;
    cursor: pointer !important;
  }

  .menu-btn:hover {
    color: #e01e5a !important;
  }

  /* Improved dropdown menus */
  .dropdown-menu {
    position: static !important;
    display: block !important;
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
    padding-left: 20px !important;
  }

  .dropdown-menu a {
    padding: 8px 15px !important;
    font-size: 16px !important;
    color: #666 !important;
  }

  /* Better logo sizing */
  .logo img,
  .logo-img {
    max-height: 60px !important;
    width: auto !important;
  }
}

/* Improved forms for mobile */
@media (max-width: 768px) {
  .form-group {
    margin-bottom: 20px !important;
  }

  .form-label {
    font-size: 16px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    color: #333 !important;
  }

  .form-control:focus {
    border-color: #fd226a !important;
    box-shadow: 0 0 0 0.2rem rgba(253, 34, 106, 0.25) !important;
  }

  /* Better select dropdowns */
  select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 12px center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 12px !important;
    padding-right: 40px !important;
  }
}

/* Better hero section for mobile */
@media (max-width: 768px) {
  .home-img {
    min-height: 50vh !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-attachment: scroll !important;
  }

  .home-img h1 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5) !important;
    margin: 0 !important;
  }
}

/* Improved footer for mobile */
@media (max-width: 768px) {
  footer .col-md-4 {
    text-align: center !important;
    margin-bottom: 30px !important;
  }

  footer h5 {
    font-size: 18px !important;
    margin-bottom: 15px !important;
  }

  footer p,
  footer a {
    font-size: 14px !important;
    line-height: 1.5 !important;
  }

  footer .list-unstyled li {
    margin-bottom: 8px !important;
  }
}

/* Performance optimizations for mobile */
@media (max-width: 768px) {
  /* Reduce animations on mobile for better performance */
  * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }

  /* Optimize images */
  img {
    image-rendering: optimizeQuality !important;
  }

  /* Better scrolling - Modern approach */
  html {
    scroll-behavior: smooth !important;
  }

  /* Smooth scrolling for mobile */
  body {
    overscroll-behavior: contain !important;
  }
}

/* ===== ENHANCED USER EXPERIENCE FOR SMALL SCREENS ===== */

/* Improved accessibility and usability */
@media (max-width: 768px) {
  /* Better focus indicators */
  a:focus,
  button:focus,
  input:focus,
  textarea:focus,
  select:focus {
    outline: 2px solid #fd226a !important;
    outline-offset: 2px !important;
  }

  /* Improved link styling */
  a {
    text-decoration: none !important;
    transition: color 0.3s ease !important;
  }

  a:hover {
    text-decoration: underline !important;
  }

  /* Better spacing for clickable elements */
  .nav-link,
  .btn,
  .card-link {
    min-height: 48px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Improved error messages */
  .alert {
    border-radius: 10px !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
    font-size: 16px !important;
  }

  /* Better loading states */
  .btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
  }

  /* Improved tooltips for mobile */
  .tooltip {
    font-size: 14px !important;
  }
}

/* Enhanced card interactions */
@media (max-width: 768px) {
  .card {
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    cursor: pointer !important;
  }

  .card:active {
    transform: scale(0.98) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15) !important;
  }

  /* Better card content spacing */
  .card-title {
    font-size: 18px !important;
    margin-bottom: 15px !important;
    line-height: 1.4 !important;
  }

  .card-text {
    font-size: 14px !important;
    line-height: 1.5 !important;
    color: #666 !important;
  }
}

/* Improved navigation experience */
@media (max-width: 768px) {
  /* Sticky navigation */
  .navbar,
  .navv-bar {
    position: sticky !important;
    top: 0 !important;
    z-index: 1000 !important;
    background: rgba(255, 255, 255, 0.95) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  }

  /* Better breadcrumbs */
  .breadcrumb {
    background: transparent !important;
    padding: 10px 0 !important;
    font-size: 14px !important;
  }

  .breadcrumb-item + .breadcrumb-item::before {
    content: ">" !important;
    color: #666 !important;
  }
}

/* Enhanced form experience */
@media (max-width: 768px) {
  /* Better form validation */
  .form-control.is-invalid {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
  }

  .form-control.is-valid {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
  }

  /* Better checkbox and radio styling */
  .form-check-input {
    width: 20px !important;
    height: 20px !important;
    margin-top: 0 !important;
  }

  .form-check-label {
    font-size: 16px !important;
    margin-left: 10px !important;
  }

  /* Improved file upload */
  .form-control[type="file"] {
    padding: 10px !important;
    border: 2px dashed #ddd !important;
    background: #f8f9fa !important;
  }
}

/* Better modal experience */
@media (max-width: 768px) {
  .modal {
    padding: 0 !important;
  }

  .modal-dialog {
    height: 100vh !important;
    margin: 0 !important;
    max-width: 100% !important;
    display: flex !important;
    align-items: center !important;
  }

  .modal-content {
    border: none !important;
    border-radius: 0 !important;
    max-height: 90vh !important;
    overflow-y: auto !important;
  }

  .modal-header {
    padding: 20px !important;
    border-bottom: 1px solid #eee !important;
  }

  .modal-body {
    padding: 20px !important;
  }

  .modal-footer {
    padding: 20px !important;
    border-top: 1px solid #eee !important;
  }
}
